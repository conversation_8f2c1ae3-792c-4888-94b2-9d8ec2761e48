import { useState } from "react";
import OutlineGenerator from "@/components/book/OutlineGenerator";
import ContentGenerator from "@/components/book/ContentGenerator";
import { 
  BookOutline, 
  Chapter, 
  GeneratedContent, 
  OutlineGenerationParams,
  WritingTone,
  WritingStyle,
  WritingLanguage
} from "@shared/types";

export default function AppPage() {
  // Book content state
  const [bookTopic, setBookTopic] = useState<string>("");
  const [outline, setOutline] = useState<BookOutline | null>(null);
  const [selectedChapter, setSelectedChapter] = useState<{
    mainChapterTitle: string;
    subChapterTitle: string;
    index: number;
    chapterIndex: number;
    subChapterIndex: number;
  } | null>(null);
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);
  const [generatedChapters, setGeneratedChapters] = useState<Record<string, boolean>>({});

  // Generation parameters state
  const [outlineParams, setOutlineParams] = useState<OutlineGenerationParams>({
    maxChapters: 10,
    maxSubChapters: 5
  });
  
  const [contentTone, setContentTone] = useState<WritingTone>("professional");
  const [contentStyle, setContentStyle] = useState<WritingStyle>("descriptive");
  const [contentLanguage, setContentLanguage] = useState<WritingLanguage>("intermediate");

  const handleOutlineGenerated = (newOutline: BookOutline) => {
    setOutline(newOutline);
    setSelectedChapter(null);
    setGeneratedContent(null);
  };

  // Track the next chapter to be generated (sequential order)
  const [nextChapterToGenerate, setNextChapterToGenerate] = useState<{
    chapterIndex: number;
    subChapterIndex: number;
  }>({ chapterIndex: 0, subChapterIndex: 0 });

  const handleChapterSelect = (mainChapterTitle: string, subChapterTitle: string, index: number, chapterIndex: number, subChapterIndex: number) => {
    // Check if this chapter has already been generated
    const isGenerated = generatedChapters[`${mainChapterTitle}-${subChapterTitle}`];
    
    // If it's already generated, just show the content without regenerating
    if (isGenerated) {
      // Find the existing content
      const existingContent = outline?.chapters.flatMap((chapter, cIdx) => 
        chapter.subchapters.map((subchapter, sIdx) => ({
          mainChapterTitle: chapter.title,
          subChapterTitle: subchapter,
          index: cIdx + 1,
          chapterIndex: cIdx,
          subChapterIndex: sIdx,
          content: generatedChapters[`${chapter.title}-${subchapter}`] ? 
            localStorage.getItem(`chapter-content-${chapter.title}-${subchapter}`) || '' : ''
        }))
      ).find(c => c.mainChapterTitle === mainChapterTitle && c.subChapterTitle === subChapterTitle);
      
      if (existingContent && existingContent.content) {
        setSelectedChapter({
          mainChapterTitle,
          subChapterTitle,
          index,
          chapterIndex,
          subChapterIndex
        });
        
        setGeneratedContent({
          mainChapterTitle,
          subChapterTitle,
          content: existingContent.content,
          index
        });
        return;
      }
    }
    
    // Check if this is the next chapter in sequence
    const isNextInSequence = 
      (chapterIndex === nextChapterToGenerate.chapterIndex && 
       subChapterIndex === nextChapterToGenerate.subChapterIndex);
    
    // Only allow generation if it's the next in sequence
    if (isNextInSequence) {
      // Reset generated content when selecting a new chapter
      setGeneratedContent(null);
      setSelectedChapter({
        mainChapterTitle,
        subChapterTitle,
        index,
        chapterIndex,
        subChapterIndex
      });
    } else if (!isGenerated) {
      // If trying to select a chapter out of sequence that hasn't been generated yet
      toast({
        title: "Sequential generation required",
        description: "Please generate content for chapters in sequential order.",
        variant: "destructive"
      });
    }
  };

  const handleContentGenerated = (content: string) => {
    if (selectedChapter) {
      const newContent: GeneratedContent = {
        mainChapterTitle: selectedChapter.mainChapterTitle,
        subChapterTitle: selectedChapter.subChapterTitle,
        content,
        index: selectedChapter.index
      };
      
      setGeneratedContent(newContent);
      
      // Store the content in localStorage for retrieval later
      localStorage.setItem(
        `chapter-content-${selectedChapter.mainChapterTitle}-${selectedChapter.subChapterTitle}`, 
        content
      );
      
      // Mark this chapter as generated
      setGeneratedChapters(prev => ({
        ...prev,
        [`${selectedChapter.mainChapterTitle}-${selectedChapter.subChapterTitle}`]: true
      }));
      
      // Update the next chapter to generate
      if (outline) {
        let nextChapterIndex = selectedChapter.chapterIndex;
        let nextSubChapterIndex = selectedChapter.subChapterIndex + 1;
        
        // If we've reached the end of subchapters in this chapter, move to the next chapter
        if (nextSubChapterIndex >= outline.chapters[nextChapterIndex].subchapters.length) {
          nextChapterIndex++;
          nextSubChapterIndex = 0;
        }
        
        // Only update if we haven't reached the end of all chapters
        if (nextChapterIndex < outline.chapters.length) {
          setNextChapterToGenerate({
            chapterIndex: nextChapterIndex,
            subChapterIndex: nextSubChapterIndex
          });
        }
      }
    }
  };

  const handleOutlineParamsChange = (params: OutlineGenerationParams) => {
    setOutlineParams(params);
  };

  const getGeneratedChaptersCount = () => {
    return Object.keys(generatedChapters).length;
  };

  const getTotalSubchaptersCount = () => {
    if (!outline) return 0;
    return outline.chapters.reduce((count, chapter) => {
      return count + chapter.subchapters.length;
    }, 0);
  };

  return (
    <div className="flex-1 flex flex-col md:flex-row">
      {/* Left Panel: Input and Outline */}
      <div className="w-full md:w-2/5 lg:w-1/3 glass-card border-r border-glass-subtle">
        <OutlineGenerator 
          onOutlineGenerated={handleOutlineGenerated}
          onBookTopicChange={setBookTopic}
          bookTopic={bookTopic}
          outline={outline}
          onChapterSelect={handleChapterSelect}
          generatedChapters={generatedChapters}
          generatedCount={getGeneratedChaptersCount()}
          totalCount={getTotalSubchaptersCount()}
          outlineParams={outlineParams}
          onOutlineParamsChange={handleOutlineParamsChange}
          contentTone={contentTone}
          onContentToneChange={setContentTone}
          contentStyle={contentStyle}
          onContentStyleChange={setContentStyle}
          contentLanguage={contentLanguage}
          onContentLanguageChange={setContentLanguage}
          nextChapterToGenerate={nextChapterToGenerate}
        />
      </div>

      {/* Right Panel: Content Display */}
      <div className="w-full md:w-3/5 lg:w-2/3 bg-background/50 backdrop-blur-sm flex flex-col">
        <ContentGenerator
          selectedChapter={selectedChapter}
          bookTopic={bookTopic}
          generatedContent={generatedContent}
          onContentGenerated={handleContentGenerated}
          contentTone={contentTone}
          contentStyle={contentStyle}
          contentLanguage={contentLanguage}
          outline={outline}
          generatedChapters={generatedChapters}
          onChapterSelect={handleChapterSelect}
          nextChapterToGenerate={nextChapterToGenerate}
        />
      </div>
    </div>
  );
}
