import { useState } from "react";
import OutlineGenerator from "@/components/book/OutlineGenerator";
import ContentGenerator from "@/components/book/ContentGenerator";
import { 
  BookOutline, 
  Chapter, 
  GeneratedContent, 
  OutlineGenerationParams,
  WritingTone,
  WritingStyle,
  WritingLanguage
} from "@shared/types";

export default function AppPage() {
  // Book content state
  const [bookTopic, setBookTopic] = useState<string>("");
  const [outline, setOutline] = useState<BookOutline | null>(null);
  const [selectedChapter, setSelectedChapter] = useState<{
    mainChapterTitle: string;
    subChapterTitle: string;
    index: number;
  } | null>(null);
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);
  const [generatedChapters, setGeneratedChapters] = useState<Record<string, boolean>>({});

  // Generation parameters state
  const [outlineParams, setOutlineParams] = useState<OutlineGenerationParams>({
    maxChapters: 10,
    maxSubChapters: 5
  });
  
  const [contentTone, setContentTone] = useState<WritingTone>("professional");
  const [contentStyle, setContentStyle] = useState<WritingStyle>("descriptive");
  const [contentLanguage, setContentLanguage] = useState<WritingLanguage>("intermediate");

  const handleOutlineGenerated = (newOutline: BookOutline) => {
    setOutline(newOutline);
    setSelectedChapter(null);
    setGeneratedContent(null);
  };

  const handleChapterSelect = (mainChapterTitle: string, subChapterTitle: string, index: number) => {
    // If selecting the same chapter that is already generated, just show it
    if (selectedChapter?.mainChapterTitle === mainChapterTitle && 
        selectedChapter?.subChapterTitle === subChapterTitle) {
      return;
    }
    
    // Reset generated content when selecting a new chapter
    setGeneratedContent(null);
    setSelectedChapter({
      mainChapterTitle,
      subChapterTitle,
      index
    });
  };

  const handleContentGenerated = (content: string) => {
    if (selectedChapter) {
      const newContent: GeneratedContent = {
        mainChapterTitle: selectedChapter.mainChapterTitle,
        subChapterTitle: selectedChapter.subChapterTitle,
        content,
        index: selectedChapter.index
      };
      
      setGeneratedContent(newContent);
      
      // Mark this chapter as generated
      setGeneratedChapters(prev => ({
        ...prev,
        [`${selectedChapter.mainChapterTitle}-${selectedChapter.subChapterTitle}`]: true
      }));
    }
  };

  const handleOutlineParamsChange = (params: OutlineGenerationParams) => {
    setOutlineParams(params);
  };

  const getGeneratedChaptersCount = () => {
    return Object.keys(generatedChapters).length;
  };

  const getTotalSubchaptersCount = () => {
    if (!outline) return 0;
    return outline.chapters.reduce((count, chapter) => {
      return count + chapter.subchapters.length;
    }, 0);
  };

  return (
    <div className="flex-1 flex flex-col md:flex-row">
      {/* Left Panel: Input and Outline */}
      <div className="w-full md:w-2/5 lg:w-1/3 glass-card border-r border-glass-subtle">
        <OutlineGenerator 
          onOutlineGenerated={handleOutlineGenerated}
          onBookTopicChange={setBookTopic}
          bookTopic={bookTopic}
          outline={outline}
          onChapterSelect={handleChapterSelect}
          generatedChapters={generatedChapters}
          generatedCount={getGeneratedChaptersCount()}
          totalCount={getTotalSubchaptersCount()}
          outlineParams={outlineParams}
          onOutlineParamsChange={handleOutlineParamsChange}
          contentTone={contentTone}
          onContentToneChange={setContentTone}
          contentStyle={contentStyle}
          onContentStyleChange={setContentStyle}
          contentLanguage={contentLanguage}
          onContentLanguageChange={setContentLanguage}
        />
      </div>

      {/* Right Panel: Content Display */}
      <div className="w-full md:w-3/5 lg:w-2/3 bg-background/50 backdrop-blur-sm flex flex-col">
        <ContentGenerator
          selectedChapter={selectedChapter}
          bookTopic={bookTopic}
          generatedContent={generatedContent}
          onContentGenerated={handleContentGenerated}
          contentTone={contentTone}
          contentStyle={contentStyle}
          contentLanguage={contentLanguage}
        />
      </div>
    </div>
  );
}
