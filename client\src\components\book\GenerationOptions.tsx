import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { InfoIcon } from "lucide-react";
import { 
  OutlineGenerationParams, 
  WritingTone, 
  WritingStyle, 
  WritingLanguage 
} from "@shared/types";

interface GenerationOptionsProps {
  outlineParams: OutlineGenerationParams;
  onOutlineParamsChange: (params: OutlineGenerationParams) => void;
  contentTone: WritingTone;
  onContentToneChange: (tone: WritingTone) => void;
  contentStyle: WritingStyle;
  onContentStyleChange: (style: WritingStyle) => void;
  contentLanguage: WritingLanguage;
  onContentLanguageChange: (language: WritingLanguage) => void;
}

export default function GenerationOptions({
  outlineParams,
  onOutlineParamsChange,
  contentTone,
  onContentToneChange,
  contentStyle,
  onContentStyleChange,
  contentLanguage,
  onContentLanguageChange
}: GenerationOptionsProps) {
  const [isOpen, setIsOpen] = useState<string[]>([]);

  const updateOutlineParams = (field: keyof OutlineGenerationParams, value: any) => {
    onOutlineParamsChange({
      ...outlineParams,
      [field]: value
    });
  };

  const validateNumberInput = (value: string, min: number, max: number, field: keyof OutlineGenerationParams) => {
    const numValue = parseInt(value);
    if (!isNaN(numValue) && numValue >= min && numValue <= max) {
      updateOutlineParams(field, numValue);
    }
  };

  return (
    <Accordion 
      type="multiple" 
      value={isOpen} 
      onValueChange={setIsOpen}
      className="border rounded-md divide-y"
    >
      <AccordionItem value="outline-options" className="border-none">
        <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-muted/20">
          <div className="flex items-center text-left">
            <svg className="h-5 w-5 mr-2 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M4 5h16a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1z" />
              <path d="M4 9h16" />
              <path d="M4 14h16" />
              <path d="M4 19h16" />
            </svg>
            <span>Outline Structure</span>
          </div>
        </AccordionTrigger>
        <AccordionContent className="px-4 pb-4 pt-1">
          <div className="grid grid-cols-2 gap-4 mt-2">
            <div>
              <div className="flex items-center mb-1">
                <Label htmlFor="min-chapters" className="text-sm mr-2">
                  Min Chapters
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-[180px] text-sm">Minimum number of chapters (7-20)</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Input
                id="min-chapters"
                type="number"
                min={7}
                max={20}
                value={outlineParams.minChapters}
                onChange={(e) => validateNumberInput(e.target.value, 7, 20, 'minChapters')}
                className="w-full"
              />
            </div>
            <div>
              <div className="flex items-center mb-1">
                <Label htmlFor="max-chapters" className="text-sm mr-2">
                  Max Chapters
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-[180px] text-sm">Maximum number of chapters (7-20)</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Input
                id="max-chapters"
                type="number"
                min={outlineParams.minChapters}
                max={20}
                value={outlineParams.maxChapters}
                onChange={(e) => validateNumberInput(e.target.value, outlineParams.minChapters, 20, 'maxChapters')}
                className="w-full"
              />
            </div>
            <div>
              <div className="flex items-center mb-1">
                <Label htmlFor="min-subchapters" className="text-sm mr-2">
                  Min Subchapters
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-[180px] text-sm">Minimum subchapters per chapter (3-7)</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Input
                id="min-subchapters"
                type="number"
                min={3}
                max={7}
                value={outlineParams.minSubChapters}
                onChange={(e) => validateNumberInput(e.target.value, 3, 7, 'minSubChapters')}
                className="w-full"
              />
            </div>
            <div>
              <div className="flex items-center mb-1">
                <Label htmlFor="max-subchapters" className="text-sm mr-2">
                  Max Subchapters
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-[180px] text-sm">Maximum subchapters per chapter (3-7)</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Input
                id="max-subchapters"
                type="number"
                min={outlineParams.minSubChapters}
                max={7}
                value={outlineParams.maxSubChapters}
                onChange={(e) => validateNumberInput(e.target.value, outlineParams.minSubChapters, 7, 'maxSubChapters')}
                className="w-full"
              />
            </div>
          </div>
        </AccordionContent>
      </AccordionItem>

      <AccordionItem value="content-options" className="border-none">
        <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-muted/20">
          <div className="flex items-center text-left">
            <svg className="h-5 w-5 mr-2 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M12 19l7-7 3 3-7 7-3-3z" />
              <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z" />
              <path d="M2 2l7.586 7.586" />
              <path d="M11 11l4 4" />
            </svg>
            <span>Writing Style</span>
          </div>
        </AccordionTrigger>
        <AccordionContent className="px-4 pb-4 pt-1">
          <div className="space-y-4 mt-2">
            <div>
              <div className="flex items-center mb-1">
                <Label htmlFor="tone" className="text-sm mr-2">
                  Tone
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-[180px] text-sm">The overall tone of the writing</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Select
                value={contentTone}
                onValueChange={(value) => onContentToneChange(value as WritingTone)}
              >
                <SelectTrigger id="tone" className="w-full">
                  <SelectValue placeholder="Select tone" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="professional">Professional</SelectItem>
                  <SelectItem value="casual">Casual</SelectItem>
                  <SelectItem value="academic">Academic</SelectItem>
                  <SelectItem value="conversational">Conversational</SelectItem>
                  <SelectItem value="instructional">Instructional</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <div className="flex items-center mb-1">
                <Label htmlFor="style" className="text-sm mr-2">
                  Style
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-[180px] text-sm">The writing style approach</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Select
                value={contentStyle}
                onValueChange={(value) => onContentStyleChange(value as WritingStyle)}
              >
                <SelectTrigger id="style" className="w-full">
                  <SelectValue placeholder="Select style" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="descriptive">Descriptive</SelectItem>
                  <SelectItem value="analytical">Analytical</SelectItem>
                  <SelectItem value="persuasive">Persuasive</SelectItem>
                  <SelectItem value="narrative">Narrative</SelectItem>
                  <SelectItem value="technical">Technical</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <div className="flex items-center mb-1">
                <Label htmlFor="language" className="text-sm mr-2">
                  Language Level
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-[180px] text-sm">The complexity of language used</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Select
                value={contentLanguage}
                onValueChange={(value) => onContentLanguageChange(value as WritingLanguage)}
              >
                <SelectTrigger id="language" className="w-full">
                  <SelectValue placeholder="Select language level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="simple">Simple</SelectItem>
                  <SelectItem value="intermediate">Intermediate</SelectItem>
                  <SelectItem value="advanced">Advanced</SelectItem>
                  <SelectItem value="technical">Technical</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}