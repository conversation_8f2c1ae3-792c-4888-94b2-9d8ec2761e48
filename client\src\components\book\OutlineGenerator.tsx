import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { BookOutline, OutlineGenerationParams, WritingTone, WritingStyle, WritingLanguage } from "@shared/types";
import { apiRequest } from "@/lib/queryClient";
import { Textarea } from "@/components/ui/textarea";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { CheckIcon, EyeIcon, Settings2Icon, ArrowRightIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import GenerationOptions from "./GenerationOptions";

interface OutlineGeneratorProps {
  onOutlineGenerated: (outline: BookOutline) => void;
  onBookTopicChange: (topic: string) => void;
  bookTopic: string;
  outline: BookOutline | null;
  onChapterSelect: (mainChapterTitle: string, subChapterTitle: string, index: number, chapterIndex: number, subChapterIndex: number) => void;
  generatedChapters: Record<string, boolean>;
  generatedCount: number;
  totalCount: number;
  outlineParams: OutlineGenerationParams;
  onOutlineParamsChange: (params: OutlineGenerationParams) => void;
  contentTone: WritingTone;
  onContentToneChange: (tone: WritingTone) => void;
  contentStyle: WritingStyle;
  onContentStyleChange: (style: WritingStyle) => void;
  contentLanguage: WritingLanguage;
  onContentLanguageChange: (language: WritingLanguage) => void;
}

export default function OutlineGenerator({
  onOutlineGenerated,
  onBookTopicChange,
  bookTopic,
  outline,
  onChapterSelect,
  generatedChapters,
  generatedCount,
  totalCount,
  outlineParams,
  onOutlineParamsChange,
  contentTone,
  onContentToneChange,
  contentStyle,
  onContentStyleChange,
  contentLanguage,
  onContentLanguageChange
}: OutlineGeneratorProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showOptions, setShowOptions] = useState(false);
  const { toast } = useToast();

  const generateOutline = async () => {
    if (!bookTopic?.trim()) {
      toast({
        title: "Input required",
        description: "Please enter a book topic or keywords",
        variant: "destructive"
      });
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const res = await apiRequest('POST', '/api/generate-outline', { 
        userInput: bookTopic,
        generationParams: outlineParams
      });
      const data = await res.json();
      onOutlineGenerated(data.outline);
      toast({
        title: "Outline generated",
        description: "Your book outline has been created successfully",
      });
    } catch (err) {
      console.error("Error generating outline:", err);
      setError("Failed to generate outline. Please try again.");
      toast({
        title: "Generation failed",
        description: "Could not generate outline. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4">
      {/* Topic Input */}
      <div className="mb-4">
        <label htmlFor="book-topic" className="block text-sm font-medium text-muted-foreground mb-1">
          Book Topic or Keywords
        </label>
        <Textarea
          id="book-topic"
          className="w-full resize-none"
          rows={3}
          placeholder="Enter your book topic or keywords here..."
          value={bookTopic}
          onChange={(e) => onBookTopicChange(e.target.value)}
        />
        
        <div className="mt-2 flex flex-col space-y-2">
          <Button 
            className="w-full" 
            onClick={generateOutline}
            disabled={isLoading}
          >
            {isLoading ? "Generating..." : "Generate Outline"}
          </Button>
          
          <Button 
            variant="outline" 
            type="button" 
            className="w-full flex items-center justify-center gap-1"
            onClick={() => setShowOptions(!showOptions)}
          >
            <Settings2Icon className="h-4 w-4" />
            <span>Customization Options</span>
            <ArrowRightIcon className={`h-4 w-4 ml-1 transition-transform ${showOptions ? 'rotate-90' : ''}`} />
          </Button>
        </div>
      </div>
      
      {/* Generation Options */}
      {showOptions && (
        <div className="mb-6 animate-in fade-in duration-300">
          <GenerationOptions
            outlineParams={outlineParams}
            onOutlineParamsChange={onOutlineParamsChange}
            contentTone={contentTone}
            onContentToneChange={onContentToneChange}
            contentStyle={contentStyle}
            onContentStyleChange={onContentStyleChange}
            contentLanguage={contentLanguage}
            onContentLanguageChange={onContentLanguageChange}
          />
        </div>
      )}
      
      {/* Outline Container */}
      <div className="mt-6 border border-muted rounded-md overflow-hidden">
        <div className="p-3 bg-muted/30 flex justify-between items-center">
          <h3 className="font-medium">Book Outline</h3>
          {outline && (
            <div className="text-xs text-muted-foreground px-2 py-1 bg-background rounded-md">
              <span>{generatedCount}/{totalCount}</span> chapters generated
            </div>
          )}
        </div>
        
        {/* Loading State for Outline */}
        {isLoading && (
          <div className="py-12 flex justify-center">
            <div className="animate-spin h-8 w-8 border-4 border-primary/30 border-t-primary rounded-full"></div>
          </div>
        )}
        
        {/* Error State for Outline */}
        {error && !isLoading && (
          <div className="p-6 text-center">
            <div className="bg-destructive/20 p-4 rounded-md inline-block">
              <svg className="h-6 w-6 text-destructive mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="mt-2 text-destructive text-sm">{error}</p>
            </div>
          </div>
        )}
        
        {/* Empty State for Outline */}
        {!outline && !isLoading && !error && (
          <div className="py-10 text-center">
            <svg className="h-12 w-12 text-muted-foreground mx-auto mb-3 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p className="text-muted-foreground">Enter your topic and generate a book outline</p>
          </div>
        )}
        
        {/* Accordion for Outline */}
        {outline && !isLoading && (
          <Accordion type="multiple" className="w-full">
            {outline.chapters.map((chapter, chapterIndex) => (
              <AccordionItem key={`chapter-${chapterIndex}`} value={`chapter-${chapterIndex}`}>
                <AccordionTrigger className="px-4 py-3 bg-card/50 hover:bg-muted/20">
                  <span className="font-medium text-left">
                    {chapterIndex + 1}. {chapter.title}
                  </span>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="p-4 pt-2 bg-card border-t border-muted">
                    <ul className="space-y-1">
                      {chapter.subchapters.map((subchapter, subIndex) => {
                        const isGenerated = generatedChapters[`${chapter.title}-${subchapter}`];
                        return (
                          <li 
                            key={`subchapter-${chapterIndex}-${subIndex}`}
                            className={`flex justify-between items-center p-2 rounded group ${generatedChapters[`${chapter.title}-${subchapter}`] || (chapterIndex === 0 && subIndex === 0) ? 'cursor-pointer hover:bg-background' : 'opacity-50 cursor-not-allowed'}`}
                            onClick={() => {
                              // Check if this chapter is already generated or is the next in sequence
                              const isGenerated = generatedChapters[`${chapter.title}-${subchapter}`];
                              const isFirstChapter = chapterIndex === 0 && subIndex === 0;
                              
                              // Find the last generated chapter to determine what's next
                              let lastGeneratedChapterIndex = -1;
                              let lastGeneratedSubchapterIndex = -1;
                              let foundNext = false;
                              
                              // Scan through all chapters to find the last generated one
                              for (let cIdx = 0; cIdx < outline!.chapters.length; cIdx++) {
                                const currentChapter = outline!.chapters[cIdx];
                                for (let sIdx = 0; sIdx < currentChapter.subchapters.length; sIdx++) {
                                  const currentSubchapter = currentChapter.subchapters[sIdx];
                                  
                                  if (generatedChapters[`${currentChapter.title}-${currentSubchapter}`]) {
                                    lastGeneratedChapterIndex = cIdx;
                                    lastGeneratedSubchapterIndex = sIdx;
                                  }
                                }
                              }
                              
                              // Determine the next chapter to generate
                              let nextChapterIndex = 0;
                              let nextSubchapterIndex = 0;
                              
                              if (lastGeneratedChapterIndex !== -1) {
                                nextChapterIndex = lastGeneratedChapterIndex;
                                nextSubchapterIndex = lastGeneratedSubchapterIndex + 1;
                                
                                // If we've reached the end of subchapters in this chapter, move to the next chapter
                                if (nextSubchapterIndex >= outline!.chapters[nextChapterIndex].subchapters.length) {
                                  nextChapterIndex++;
                                  nextSubchapterIndex = 0;
                                }
                              }
                              
                              // Check if this is the next chapter in sequence or already generated
                              const isNextInSequence = chapterIndex === nextChapterIndex && subIndex === nextSubchapterIndex;
                              
                              if (isGenerated || isNextInSequence || isFirstChapter) {
                                onChapterSelect(chapter.title, subchapter, chapterIndex + 1, chapterIndex, subIndex);
                              } else {
                                // Show toast for sequential generation requirement
                                toast({
                                  title: "Sequential generation required",
                                  description: "Please generate content for chapters in sequential order.",
                                  variant: "destructive"
                                });
                              }
                            }}
                          >
                            <span className="text-sm">
                              {chapterIndex + 1}.{subIndex + 1} {subchapter}
                            </span>
                            <div className="flex space-x-1">
                              {isGenerated ? (
                                <span className="text-green-500">
                                  <CheckIcon className="h-5 w-5" />
                                </span>
                              ) : (
                                <>
                                  {(() => {
                                    // Find the last generated chapter to determine what's next
                                    let lastGeneratedChapterIndex = -1;
                                    let lastGeneratedSubchapterIndex = -1;
                                    
                                    // Scan through all chapters to find the last generated one
                                    for (let cIdx = 0; cIdx < outline!.chapters.length; cIdx++) {
                                      const currentChapter = outline!.chapters[cIdx];
                                      for (let sIdx = 0; sIdx < currentChapter.subchapters.length; sIdx++) {
                                        const currentSubchapter = currentChapter.subchapters[sIdx];
                                        
                                        if (generatedChapters[`${currentChapter.title}-${currentSubchapter}`]) {
                                          lastGeneratedChapterIndex = cIdx;
                                          lastGeneratedSubchapterIndex = sIdx;
                                        }
                                      }
                                    }
                                    
                                    // Determine the next chapter to generate
                                    let nextChapterIndex = 0;
                                    let nextSubchapterIndex = 0;
                                    
                                    if (lastGeneratedChapterIndex !== -1) {
                                      nextChapterIndex = lastGeneratedChapterIndex;
                                      nextSubchapterIndex = lastGeneratedSubchapterIndex + 1;
                                      
                                      // If we've reached the end of subchapters in this chapter, move to the next chapter
                                      if (nextSubchapterIndex >= outline!.chapters[nextChapterIndex].subchapters.length) {
                                        nextChapterIndex++;
                                        nextSubchapterIndex = 0;
                                      }
                                    }
                                    
                                    // Check if this is the next chapter in sequence
                                    const isNextInSequence = chapterIndex === nextChapterIndex && subIndex === nextSubchapterIndex;
                                    const isFirstChapter = chapterIndex === 0 && subIndex === 0;
                                    
                                    if (isNextInSequence || (isFirstChapter && !Object.keys(generatedChapters).length)) {
                                      return (
                                        <div className="flex items-center">
                                          <span className="text-primary/70 group-hover:inline-block">
                                            <EyeIcon className="h-5 w-5" />
                                          </span>
                                          <span className="ml-1 text-xs text-blue-400 font-medium">Next</span>
                                        </div>
                                      );
                                    } else {
                                      return (
                                        <span className="text-muted-foreground">
                                          <EyeIcon className="h-5 w-5 opacity-30" />
                                        </span>
                                      );
                                    }
                                  })()} 
                                </>
                              )}
                            </div>
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        )}
      </div>
    </div>
  );
}
