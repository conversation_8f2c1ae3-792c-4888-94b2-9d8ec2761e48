import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { BookOutline, OutlineGenerationParams, WritingTone, WritingStyle, WritingLanguage } from "@shared/types";
import { apiRequest } from "@/lib/queryClient";
import { Textarea } from "@/components/ui/textarea";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { CheckIcon, EyeIcon, Settings2Icon, ArrowRightIcon, Loader2Icon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import GenerationOptions from "./GenerationOptions";

interface OutlineGeneratorProps {
  onOutlineGenerated: (outline: BookOutline) => void;
  onBookTopicChange: (topic: string) => void;
  bookTopic: string;
  outline: BookOutline | null;
  onChapterSelect: (mainChapterTitle: string, subChapterTitle: string, index: number, chapterIndex: number, subChapterIndex: number) => void;
  generatedChapters: Record<string, boolean>;
  generatedCount: number;
  totalCount: number;
  outlineParams: OutlineGenerationParams;
  onOutlineParamsChange: (params: OutlineGenerationParams) => void;
  contentTone: WritingTone;
  onContentToneChange: (tone: WritingTone) => void;
  contentStyle: WritingStyle;
  onContentStyleChange: (style: WritingStyle) => void;
  contentLanguage: WritingLanguage;
  onContentLanguageChange: (language: WritingLanguage) => void;
  nextChapterToGenerate?: { chapterIndex: number; subChapterIndex: number };
}

export default function OutlineGenerator({
  onOutlineGenerated,
  onBookTopicChange,
  bookTopic,
  outline,
  onChapterSelect,
  generatedChapters,
  generatedCount,
  totalCount,
  outlineParams,
  onOutlineParamsChange,
  contentTone,
  onContentToneChange,
  contentStyle,
  onContentStyleChange,
  contentLanguage,
  onContentLanguageChange,
  nextChapterToGenerate
}: OutlineGeneratorProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showOptions, setShowOptions] = useState(false);
  const [generatingAllSubchapters, setGeneratingAllSubchapters] = useState(false);
  const [currentGeneratingChapter, setCurrentGeneratingChapter] = useState<number | null>(null);
  const [generationProgress, setGenerationProgress] = useState<{current: number, total: number} | null>(null);
  const [generationError, setGenerationError] = useState<{chapterIndex: number, subChapterIndex: number} | null>(null);
  const { toast } = useToast();

  const generateOutline = async () => {
    if (!bookTopic) {
      toast({
        title: "Topic required",
        description: "Please enter a book topic before generating an outline.",
        variant: "destructive"
      });
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const res = await apiRequest('POST', '/api/generate-outline', {
        topic: bookTopic,
        params: outlineParams
      });
      
      const data = await res.json();
      onOutlineGenerated(data.outline);
      
      toast({
        title: "Outline generated",
        description: "Your book outline has been created successfully",
      });
    } catch (err) {
      console.error("Error generating outline:", err);
      setError("Failed to generate outline. Please try again.");
      
      toast({
        title: "Generation failed",
        description: "Could not generate book outline. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to handle generating all subchapters for a specific chapter
  const generateAllSubchapters = async (chapterIndex: number, startFromSubChapterIndex?: number) => {
    if (!outline) return;
    
    const chapter = outline.chapters[chapterIndex];
    if (!chapter) return;
    
    // If we're retrying from a specific subchapter, use that index
    // Otherwise, find the first non-generated subchapter
    let nextSubchapterIndex = startFromSubChapterIndex !== undefined
      ? startFromSubChapterIndex
      : chapter.subchapters.findIndex(
          (subchapter) => !generatedChapters[`${chapter.title}-${subchapter}`]
        );
    
    // Check if all subchapters are already generated
    if (nextSubchapterIndex === -1 || nextSubchapterIndex >= chapter.subchapters.length) {
      toast({
        title: "Already generated",
        description: "All subchapters in this chapter have already been generated."
      });
      return;
    }
    
    // Check if we're already generating subchapters
    if (generatingAllSubchapters && !startFromSubChapterIndex) {
      toast({
        title: "Generation in progress",
        description: "Please wait for the current generation to complete."
      });
      return;
    }
    
    // Reset error state
    setGenerationError(null);
    
    // Set state to indicate bulk generation is in progress
    setGeneratingAllSubchapters(true);
    setCurrentGeneratingChapter(chapterIndex);
    
    // Calculate total subchapters to generate
    const totalToGenerate = chapter.subchapters.length - nextSubchapterIndex;
    let currentProgress = 1;
    
    setGenerationProgress({
      current: currentProgress,
      total: totalToGenerate
    });
    
    // Start the generation process by selecting the first non-generated subchapter
    // The actual content generation will be handled by the ContentGenerator component
    onChapterSelect(
      chapter.title,
      chapter.subchapters[nextSubchapterIndex],
      chapterIndex + 1,
      chapterIndex,
      nextSubchapterIndex
    );
    
    // Track the last time we saw progress
    let lastProgressTime = Date.now();
    let consecutiveNoProgressCount = 0;
    
    // Set up an interval to check for generation completion and move to the next subchapter
    const checkInterval = setInterval(() => {
      // Check if the current subchapter has been generated
      if (generatedChapters[`${chapter.title}-${chapter.subchapters[nextSubchapterIndex]}`]) {
        // Reset error tracking since we made progress
        consecutiveNoProgressCount = 0;
        lastProgressTime = Date.now();
        
        // Update progress
        currentProgress++;
        setGenerationProgress({
          current: currentProgress,
          total: totalToGenerate
        });
        
        // Move to the next subchapter
        nextSubchapterIndex++;
        
        // Check if we've completed all subchapters
        if (nextSubchapterIndex >= chapter.subchapters.length) {
          clearInterval(checkInterval);
          setGeneratingAllSubchapters(false);
          setCurrentGeneratingChapter(null);
          setGenerationProgress(null);
          
          toast({
            title: "Generation complete",
            description: `All subchapters in "${chapter.title}" have been generated.`
          });
          return;
        }
        
        // Skip already generated subchapters
        while (
          nextSubchapterIndex < chapter.subchapters.length && 
          generatedChapters[`${chapter.title}-${chapter.subchapters[nextSubchapterIndex]}`]
        ) {
          nextSubchapterIndex++;
          currentProgress++;
        }
        
        // If we've reached the end, stop the process
        if (nextSubchapterIndex >= chapter.subchapters.length) {
          clearInterval(checkInterval);
          setGeneratingAllSubchapters(false);
          setCurrentGeneratingChapter(null);
          setGenerationProgress(null);
          
          toast({
            title: "Generation complete",
            description: `All subchapters in "${chapter.title}" have been generated.`
          });
          return;
        }
        
        // Select the next subchapter to generate
        onChapterSelect(
          chapter.title,
          chapter.subchapters[nextSubchapterIndex],
          chapterIndex + 1,
          chapterIndex,
          nextSubchapterIndex
        );
      } else {
        // Check for stalled generation (no progress for 30 seconds)
        const now = Date.now();
        if (now - lastProgressTime > 30000) { // 30 seconds
          consecutiveNoProgressCount++;
          lastProgressTime = now; // Reset timer
          
          // After 3 consecutive no-progress checks (90 seconds total), mark as error
          if (consecutiveNoProgressCount >= 3) {
            clearInterval(checkInterval);
            setGenerationError({ chapterIndex, subChapterIndex: nextSubchapterIndex });
            setGeneratingAllSubchapters(false);
            setCurrentGeneratingChapter(null);
            
            // Keep progress info for retry UI
            toast({
              title: "Generation stalled",
              description: `Generation of "${chapter.subchapters[nextSubchapterIndex]}" failed. You can retry from this point.`,
              variant: "destructive"
            });
            return;
          }
        }
      }
    }, 1000); // Check every second
    
    // Set up a timeout to stop the process if it takes too long (5 minutes)
    setTimeout(() => {
      if (generatingAllSubchapters && currentGeneratingChapter === chapterIndex) {
        clearInterval(checkInterval);
        setGenerationError({ chapterIndex, subChapterIndex: nextSubchapterIndex });
        setGeneratingAllSubchapters(false);
        setCurrentGeneratingChapter(null);
        
        toast({
          title: "Generation timeout",
          description: "The generation process took too long and was stopped. You can retry from the failed point.",
          variant: "destructive"
        });
      }
    }, 5 * 60 * 1000);
  };
  
  // Function to retry generation from a specific point
  const retryFailedGeneration = () => {
    if (!generationError) return;
    
    generateAllSubchapters(generationError.chapterIndex, generationError.subChapterIndex);
  };

  return (
    <div className="p-4">
      {/* Topic Input */}
      <div className="mb-4">
        <label htmlFor="book-topic" className="block text-sm font-medium text-muted-foreground mb-1">
          Book Topic or Keywords
        </label>
        <Textarea
          id="book-topic"
          className="w-full resize-none"
          rows={3}
          placeholder="Enter your book topic or keywords here..."
          value={bookTopic}
          onChange={(e) => onBookTopicChange(e.target.value)}
        />
        
        <div className="mt-2 flex flex-col space-y-2">
          <Button 
            className="w-full" 
            onClick={generateOutline}
            disabled={isLoading}
          >
            {isLoading ? "Generating..." : "Generate Outline"}
          </Button>
          
          <Button 
            variant="outline" 
            type="button" 
            className="w-full flex items-center justify-center gap-1"
            onClick={() => setShowOptions(!showOptions)}
          >
            <Settings2Icon className="h-4 w-4" />
            <span>Customization Options</span>
            <ArrowRightIcon className={`h-4 w-4 ml-1 transition-transform ${showOptions ? 'rotate-90' : ''}`} />
          </Button>
        </div>
      </div>
      
      {/* Generation Options */}
      {showOptions && (
        <div className="mb-6 animate-in fade-in duration-300">
          <GenerationOptions
            outlineParams={outlineParams}
            onOutlineParamsChange={onOutlineParamsChange}
            contentTone={contentTone}
            onContentToneChange={onContentToneChange}
            contentStyle={contentStyle}
            onContentStyleChange={onContentStyleChange}
            contentLanguage={contentLanguage}
            onContentLanguageChange={onContentLanguageChange}
          />
        </div>
      )}
      
      {/* Outline Container */}
      <div className="mt-6 border border-muted rounded-md overflow-hidden">
        <div className="p-3 bg-muted/30 flex justify-between items-center">
          <h3 className="font-medium">Book Outline</h3>
          {outline && (
            <div className="text-xs text-muted-foreground px-2 py-1 bg-background rounded-md">
              <span>{generatedCount}/{totalCount}</span> chapters generated
            </div>
          )}
        </div>
        
        {/* Loading State for Outline */}
        {isLoading && (
          <div className="py-12 flex justify-center">
            <div className="animate-spin h-8 w-8 border-4 border-primary/30 border-t-primary rounded-full"></div>
          </div>
        )}
        
        {/* Error State for Outline */}
        {error && !isLoading && (
          <div className="p-6 text-center">
            <div className="bg-destructive/20 p-4 rounded-md inline-block">
              <svg className="h-6 w-6 text-destructive mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="mt-2 text-destructive text-sm">{error}</p>
            </div>
          </div>
        )}
        
        {/* Empty State for Outline */}
        {!outline && !isLoading && !error && (
          <div className="py-10 text-center">
            <svg className="h-12 w-12 text-muted-foreground mx-auto mb-3 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p className="text-muted-foreground">Enter your topic and generate a book outline</p>
          </div>
        )}
        
        {/* Accordion for Outline */}
        {outline && !isLoading && (
          <Accordion type="multiple" className="w-full">
            {outline.chapters.map((chapter, chapterIndex) => (
              <AccordionItem key={`chapter-${chapterIndex}`} value={`chapter-${chapterIndex}`}>
                <AccordionTrigger className="px-4 py-3 bg-card/50 hover:bg-muted/20">
                  <div className="flex justify-between items-center w-full">
                    <span className="font-medium text-left">
                      {chapterIndex + 1}. {chapter.title}
                    </span>
                    {chapter.subchapters.length > 0 && (
                      <Button 
                        size="sm" 
                        variant={generatingAllSubchapters && currentGeneratingChapter === chapterIndex ? "default" : 
                                generationError && generationError.chapterIndex === chapterIndex ? "destructive" : "outline"}
                        className={`ml-2 text-xs ${generatingAllSubchapters && currentGeneratingChapter === chapterIndex ? 'bg-primary/20 hover:bg-primary/30' : 
                                  generationError && generationError.chapterIndex === chapterIndex ? 'hover:bg-destructive/90' : ''}`}
                        disabled={generatingAllSubchapters && currentGeneratingChapter !== chapterIndex && 
                          !(generationError && generationError.chapterIndex === chapterIndex) || 
                          (!generationError && chapter.subchapters.every(subchapter => generatedChapters[`${chapter.title}-${subchapter}`]))}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (generationError && generationError.chapterIndex === chapterIndex) {
                            retryFailedGeneration();
                          } else {
                            generateAllSubchapters(chapterIndex);
                          }
                        }}
                      >
                        {generatingAllSubchapters && currentGeneratingChapter === chapterIndex ? (
                          <div className="flex items-center">
                            <Loader2Icon className="h-3 w-3 mr-1 animate-spin" />
                            <span>
                              {generationProgress ? `Generating ${generationProgress.current}/${generationProgress.total}` : 'Generating...'}
                            </span>
                          </div>
                        ) : generationError && generationError.chapterIndex === chapterIndex ? (
                          <div className="flex items-center">
                            <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            <span>Retry Generation</span>
                          </div>
                        ) : (
                          <div className="flex items-center">
                            {chapter.subchapters.every(subchapter => generatedChapters[`${chapter.title}-${subchapter}`]) ? (
                              <>
                                <CheckIcon className="h-3 w-3 mr-1" />
                                <span>All Generated</span>
                              </>
                            ) : (
                              <span>Generate All Sub-Chapters</span>
                            )}
                          </div>
                        )}
                      </Button>
                    )}
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="p-4 pt-2 bg-card border-t border-muted">
                    <ul className="space-y-1">
                      {chapter.subchapters.map((subchapter, subIndex) => {
                        const isGenerated = generatedChapters[`${chapter.title}-${subchapter}`];
                        
                        // Determine if this subchapter is the next one to generate
                        const isNextInSequence = nextChapterToGenerate && 
                          chapterIndex === nextChapterToGenerate.chapterIndex && 
                          subIndex === nextChapterToGenerate.subChapterIndex;
                        
                        // First chapter is always enabled if no chapters have been generated
                        const isFirstChapter = chapterIndex === 0 && subIndex === 0 && Object.keys(generatedChapters).length === 0;
                        
                        // Determine if this subchapter should be enabled
                        const isEnabled = isGenerated || isNextInSequence || isFirstChapter;
                        
                        return (
                          <li 
                            key={`subchapter-${chapterIndex}-${subIndex}`}
                            className={`flex justify-between items-center p-2 rounded group ${isEnabled ? 'cursor-pointer hover:bg-background' : 'opacity-50 cursor-not-allowed'}`}
                            onClick={() => {
                              if (isGenerated) {
                                // If already generated, just display the content
                                onChapterSelect(chapter.title, subchapter, chapterIndex + 1, chapterIndex, subIndex);
                              } else if (isNextInSequence || isFirstChapter) {
                                // If it's the next in sequence, allow generation
                                onChapterSelect(chapter.title, subchapter, chapterIndex + 1, chapterIndex, subIndex);
                              } else {
                                // Show toast for sequential generation requirement
                                toast({
                                  title: "Sequential generation required",
                                  description: "Please generate content for chapters in sequential order.",
                                  variant: "destructive"
                                });
                              }
                            }}
                          >
                            <span className="text-sm">
                              {chapterIndex + 1}.{subIndex + 1} {subchapter}
                            </span>
                            <div className="flex space-x-1">
                              {isGenerated ? (
                                <span className="text-green-500">
                                  <CheckIcon className="h-5 w-5" />
                                </span>
                              ) : (
                                <>
                                  {isNextInSequence || isFirstChapter ? (
                                    <div className="flex items-center">
                                      <span className="text-primary/70 group-hover:inline-block">
                                        <EyeIcon className="h-5 w-5" />
                                      </span>
                                      <span className="ml-1 text-xs text-blue-400 font-medium">Next</span>
                                    </div>
                                  ) : (
                                    <span className="text-muted-foreground">
                                      <EyeIcon className="h-5 w-5 opacity-30" />
                                    </span>
                                  )}
                                </>
                              )}
                            </div>
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        )}
      </div>
    </div>
  );
}
